#!/usr/bin/env python3
"""
测试配置文件功能的脚本
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, 'src')

from bsa_core import BSAConfigManager

def test_config_functionality():
    """测试配置文件功能"""
    print("=== 测试配置文件功能 ===")
    
    # 显示当前环境信息
    print(f"Python可执行文件: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"是否为打包环境: {getattr(sys, 'frozen', False)}")
    
    if getattr(sys, 'frozen', False):
        print(f"_MEIPASS: {getattr(sys, '_MEIPASS', 'Not found')}")
    
    # 测试配置路径获取
    config_path = BSAConfigManager.get_config_path()
    print(f"配置文件路径: {config_path}")
    print(f"配置文件是否存在: {Path(config_path).exists()}")
    
    # 测试加载配置
    try:
        config = BSAConfigManager.load_config()
        print("✓ 配置加载成功")
        print(f"配置项数量: {len(config)}")
        print(f"测试ID: {config.get('test_id', 'Not found')}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False
    
    # 测试保存配置
    try:
        test_config = config.copy()
        test_config['test_timestamp'] = str(datetime.now())
        BSAConfigManager.save_config(test_config)
        print("✓ 配置保存成功")
    except Exception as e:
        print(f"❌ 配置保存失败: {e}")
        return False
    
    # 验证保存的配置
    try:
        reloaded_config = BSAConfigManager.load_config()
        if 'test_timestamp' in reloaded_config:
            print("✓ 配置保存和重新加载验证成功")
        else:
            print("❌ 配置保存验证失败")
            return False
    except Exception as e:
        print(f"❌ 配置重新加载失败: {e}")
        return False
    
    print("=== 所有测试通过 ===")
    return True

if __name__ == "__main__":
    from datetime import datetime
    success = test_config_functionality()
    sys.exit(0 if success else 1)
