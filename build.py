#!/usr/bin/env python3
"""
BSA应用构建脚本 - 增强版本
支持环境检查、用户交互和自动构建
"""

import sys
import subprocess
import shutil
import platform
import importlib
from pathlib import Path

def check_python_packages():
    """检查Python包是否已安装"""
    print("=== 检查Python包环境 ===")

    # 读取requirements.txt
    requirements_file = Path('requirements.txt')
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False, []

    # 解析requirements.txt
    required_packages = []
    with open(requirements_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                # 移除版本号，只保留包名
                package_name = line.split('==')[0].split('>=')[0].split('<=')[0].split('>')[0].split('<')[0].split('!=')[0]
                required_packages.append(package_name.strip())

    # 检查每个包是否已安装
    missing_packages = []
    installed_packages = []

    for package in required_packages:
        try:
            # 特殊处理一些包名映射
            import_name = package
            if package == 'python-dateutil':
                import_name = 'dateutil'
            elif package == 'browser-cookie3':
                import_name = 'browser_cookie3'

            importlib.import_module(import_name)
            installed_packages.append(package)
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")

    # 检查PyInstaller
    try:
        importlib.import_module('PyInstaller')
        print("✓ PyInstaller")
    except ImportError:
        missing_packages.append('PyInstaller')
        print("❌ PyInstaller")

    print(f"\n已安装包: {len(installed_packages)}")
    print(f"缺失包: {len(missing_packages)}")

    return len(missing_packages) == 0, missing_packages

def check_project_files():
    """检查项目文件"""
    print("\n=== 检查项目文件 ===")

    # 检查必要文件
    required_files = [
        'src/bsa_ui.py',
        'src/bsa_core.py',
        'src/config/config.json',
        'requirements.txt',
        'bsa_app.spec',
        'run.py'
    ]
    missing_files = []

    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
        else:
            print(f"✓ {file}")

    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False

    print("✓ 所有必要文件都存在")
    return True

def check_system_info():
    """检查系统信息"""
    print("=== 系统信息 ===")

    # 检查操作系统
    system = platform.system()
    print(f"操作系统: {system}")

    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")

    # 检查Python位数
    print(f"Python架构: {platform.architecture()[0]}")

    return True

def get_user_input(prompt, default='N'):
    """获取用户输入"""
    try:
        response = input(f"{prompt} [{default}]: ").strip()
        return response if response else default
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
        sys.exit(0)

def ask_clean_build_dirs():
    """询问用户是否清理构建目录"""
    print("\n=== 构建目录清理 ===")

    # 检查是否存在构建目录
    dirs_to_check = ['build', 'dist']
    existing_dirs = [d for d in dirs_to_check if Path(d).exists()]

    if not existing_dirs:
        print("✓ 没有发现需要清理的构建目录")
        return True

    print(f"发现以下构建目录: {', '.join(existing_dirs)}")
    response = get_user_input("是否清理这些目录? (y/N)", "N")

    if response.lower() in ['y', 'yes']:
        clean_build_dirs()
        return True
    else:
        print("跳过清理构建目录")
        return True

def install_missing_packages(missing_packages):
    """安装缺失的包"""
    if not missing_packages:
        return True

    print("\n=== 安装缺失的包 ===")
    print(f"需要安装的包: {', '.join(missing_packages)}")

    response = get_user_input("是否自动安装这些包? (y/N)", "N")

    if response.lower() not in ['y', 'yes']:
        print("用户选择不安装缺失的包，程序退出")
        return False

    try:
        # 安装缺失的包
        for package in missing_packages:
            if package == 'PyInstaller':
                print(f"正在安装 {package}...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
            else:
                print(f"正在安装 {package}...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', package], check=True)
            print(f"✓ {package} 安装成功")

        print("✓ 所有缺失的包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 包安装失败: {e}")
        return False

def clean_build_dirs():
    """清理构建目录"""
    print("\n=== 清理构建目录 ===")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        dir_path = Path(dir_name)
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"✓ 清理目录: {dir_name}")
    
    # 清理.pyc文件
    for pyc_file in Path('.').rglob('*.pyc'):
        pyc_file.unlink()
        print(f"✓ 清理文件: {pyc_file}")

def build_executable():
    """构建可执行文件"""
    print("\n=== 开始构建可执行文件 ===")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'bsa_app.spec']
        print(f"执行命令: {' '.join(cmd)}")
        
        # 执行构建命令，显示详细输出
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )

        # 实时显示构建输出
        if process.stdout:
            for line in process.stdout:
                print(line.rstrip())

        process.wait()
        
        if process.returncode == 0:
            print("✓ 构建成功完成")
            return True
        else:
            print(f"❌ 构建失败，退出码: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中发生错误: {e}")
        return False

def verify_build():
    """验证构建结果"""
    print("\n=== 验证构建结果 ===")
    
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 检查可执行文件
    if sys.platform.startswith('win'):
        exe_name = 'bsa_app.exe'
    else:
        exe_name = 'bsa_app'
    
    exe_path = dist_dir / exe_name
    if not exe_path.exists():
        print(f"❌ 可执行文件不存在: {exe_path}")
        return False
    
    print(f"✓ 找到可执行文件: {exe_path}")
    print(f"✓ 文件大小: {exe_path.stat().st_size / (1024*1024):.1f} MB")
    
    # 检查配置文件
    config_path = dist_dir / 'config.json'
    if config_path.exists():
        print(f"✓ 配置文件已包含: {config_path}")
    else:
        print("⚠️ 配置文件未找到，可能需要手动复制")
    
    return True

def main():
    """主构建流程"""
    system = platform.system()
    print("BSA应用构建工具 - 增强版")
    print(f"操作系统: {system}")
    print("=" * 50)

    # 检查系统信息
    check_system_info()

    # 检查项目文件
    if not check_project_files():
        print("❌ 项目文件检查失败，请确保所有必要文件存在")
        return False

    # 检查Python包环境
    packages_ok, missing_packages = check_python_packages()

    if not packages_ok:
        print(f"\n❌ 发现 {len(missing_packages)} 个缺失的包:")
        for pkg in missing_packages:
            print(f"  - {pkg}")

        # 询问是否安装缺失的包
        if not install_missing_packages(missing_packages):
            print("❌ 无法继续构建，程序退出")
            return False
    else:
        print("✅ 所有必要的包都已安装")

    # 询问是否清理构建目录
    if not ask_clean_build_dirs():
        return False

    # 构建可执行文件
    print("\n=== 开始构建 ===")
    if not build_executable():
        print("❌ 构建失败")
        return False

    # 验证构建结果
    if not verify_build():
        print("❌ 构建验证失败")
        return False

    print("\n" + "=" * 50)
    print("🎉 构建完成！")
    print(f"可执行文件位置: {Path('dist').absolute()}")

    if system == "Windows":
        exe_name = "bsa_app.exe"
    else:
        exe_name = "bsa_app"

    print("\n使用说明:")
    print("1. 进入dist目录")
    print(f"2. 运行 {exe_name}")
    print("3. 程序会自动打开浏览器访问应用")
    print("4. 如果浏览器未自动打开，请手动访问 http://localhost:8501")

    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
